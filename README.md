# MakeMeAdmin ADMX/ADML Administrative Templates

This repository contains Group Policy Administrative Templates (ADMX/ADML) for configuring MakeMeAdmin application settings via Group Policy or Microsoft Intune.

## Overview

MakeMeAdmin is a tool that allows standard users to temporarily elevate their privileges to local administrator. These administrative templates provide centralized configuration management for MakeMeAdmin through Group Policy.

## Files Included

- **MakeMeAdmin.admx** - Policy definitions file
- **MakeMeAdmin.adml** - Language resources file (English)

## Supported Policies

### 1. Session Duration (Minutes)
- **Description**: Specifies how long the user remains a local administrator (in minutes)
- **Registry Location**: `HKLM\Software\Policies\MakeMeAdmin\SessionDurationInMinutes`
- **Default Value**: 15 minutes
- **Value Type**: Decimal (REG_DWORD)
- **Configuration**: Use decimal text box to set duration

### 2. Limit to Specific Users
- **Description**: Restrict elevation privileges to specific users only
- **Registry Location**: `HKLM\Software\Policies\MakeMeAdmin\LimitToUserList`
- **Values**: 
  - `1` = Enable user restrictions
  - `0` = Disable user restrictions (default)
- **Value Type**: Decimal (REG_DWORD)
- **Configuration**: Use decimal text box (1 to enable, 0 to disable)

## Installation Instructions

### Local Group Policy Deployment

1. **Copy files to PolicyDefinitions folder:**
   ```
   Copy MakeMeAdmin.admx to: C:\Windows\PolicyDefinitions\
   Copy MakeMeAdmin.adml to: C:\Windows\PolicyDefinitions\en-US\
   ```

2. **Access policies:**
   - Open Group Policy Editor (`gpedit.msc`)
   - Navigate to: **Computer Configuration > Administrative Templates > MakeMeAdmin Settings**

### Domain Group Policy Deployment

1. **Copy files to Central Store:**
   ```
   Copy MakeMeAdmin.admx to: \\domain.com\SYSVOL\domain.com\Policies\PolicyDefinitions\
   Copy MakeMeAdmin.adml to: \\domain.com\SYSVOL\domain.com\Policies\PolicyDefinitions\en-US\
   ```

2. **Configure via Group Policy Management Console:**
   - Open Group Policy Management Console (`gpmc.msc`)
   - Edit desired Group Policy Object
   - Navigate to: **Computer Configuration > Administrative Templates > MakeMeAdmin Settings**

### Microsoft Intune Deployment

1. **Upload Administrative Templates:**
   - Sign in to Microsoft Endpoint Manager admin center
   - Go to **Devices > Configuration profiles**
   - Create new profile: **Platform: Windows 10 and later**, **Profile type: Administrative templates**
   - Upload the ADMX/ADML files
   - Configure policies under **MakeMeAdmin Settings**

2. **Assign to groups:**
   - Assign the configuration profile to appropriate device or user groups

## Policy Configuration Examples

### Example 1: Set 30-minute session duration
1. Navigate to **MakeMeAdmin Settings > Session Duration (Minutes)**
2. Set to **Enabled**
3. Enter **30** in the duration field

### Example 2: Enable user restrictions
1. Navigate to **MakeMeAdmin Settings > Limit to Specific Users**
2. Set to **Enabled**
3. Enter **1** in the value field

## Registry Impact

When policies are applied, the following registry values are created:

```
[HKEY_LOCAL_MACHINE\SOFTWARE\Policies\MakeMeAdmin]
"SessionDurationInMinutes"=dword:0000000f    ; 15 minutes (when enabled)
"LimitToUserList"=dword:00000001             ; 1 = enabled, 0 = disabled
```

## Supported Operating Systems

- Windows 10
- Windows 11
- Windows Server 2016 and later

## Validation

The ADMX/ADML files have been validated for:
- ✅ XML syntax correctness
- ✅ Schema compliance
- ✅ Namespace consistency
- ✅ String reference integrity
- ✅ Presentation reference integrity
- ✅ SupportedOn reference validity

## Troubleshooting

### Policies not appearing in Group Policy Editor
1. Verify files are copied to correct locations
2. Refresh Group Policy Editor (close and reopen)
3. Check file permissions on PolicyDefinitions folder
4. Validate XML syntax using PowerShell:
   ```powershell
   [xml]$admx = Get-Content "MakeMeAdmin.admx"
   [xml]$adml = Get-Content "MakeMeAdmin.adml"
   ```

### Policies not applying to clients
1. Run `gpupdate /force` on client machines
2. Check Group Policy processing with `gpresult /r`
3. Verify policy assignment and filtering
4. Check event logs for Group Policy errors

## Version Information

- **ADMX Schema Version**: 1.0
- **ADML Schema Version**: 1.0
- **Namespace**: MakeMeAdmin.Policy

## Support

For issues related to:
- **ADMX/ADML files**: Check this repository's issues
- **MakeMeAdmin application**: Refer to the main MakeMeAdmin project documentation
- **Group Policy deployment**: Consult Microsoft documentation

## License

These administrative templates are provided as-is for configuration management of MakeMeAdmin application.
