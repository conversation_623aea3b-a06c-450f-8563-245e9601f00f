<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources revision="1.0" schemaVersion="1.0">
  <displayName>
    <nameSpace>MakeMeAdmin.Policy</nameSpace>
    <name>MakeMeAdmin Administrative Template</name>
  </displayName>
  <resources>
    <stringTable>
      <string id="MakeMeAdminCategory">MakeMeAdmin Settings</string>
      <string id="SessionDurationInMinutes">Session Duration (Minutes)</string>
      <string id="SessionDurationInMinutes_Explain">Specifies how long the user is a local admin (in minutes). Valid range is 1-1440 minutes (24 hours).</string>
      <string id="LimitToUserList">Limit to Specific Users</string>
      <string id="LimitToUserList_Explain">When enabled, restricts elevation privileges to specific users only. When disabled, all users can request elevation.</string>
      <string id="SUPPORTED_WIN10">At least Windows 10</string>
    </stringTable>
    <presentationTable>
      <presentation id="SessionDurationInMinutes">
        <decimalTextBox refId="SessionDurationInMinutes" defaultValue="15">Duration in minutes (1-1440):</decimalTextBox>
      </presentation>
      <presentation id="LimitToUserList">
        <checkBox refId="LimitToUserList" defaultChecked="false">Enable user restrictions</checkBox>
      </presentation>
    </presentationTable>
  </resources>
</policyDefinitionResources>
