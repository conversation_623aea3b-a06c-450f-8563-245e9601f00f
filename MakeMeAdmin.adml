<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources revision="1.0" schemaVersion="1.0">
  <displayName>
    <nameSpace>MakeMeAdmin.Policy</nameSpace>
    <name>MakeMeAdmin Administrative Template</name>
  </displayName>
  <resources>
    <stringTable>
      <string id="MakeMeAdminCategory">MakeMeAdmin Settings</string>
      <string id="SessionDurationInMinutes">Session Duration (Minutes)</string>
      <string id="SessionDurationInMinutes_Explain">Specifies how long the user is a local admin (in minutes).</string>
      <string id="LimitToUserList">Limit to Specific Users</string>
      <string id="LimitToUserList_Explain">Restrict elevation to specific users only.</string>
    </stringTable>
    <presentationTable>
      <presentation id="SessionDurationInMinutes">
        <decimalTextBox refId="SessionDurationInMinutes" defaultValue="15" label="Duration in minutes"/>
      </presentation>
      <presentation id="LimitToUserList">
        <decimalTextBox refId="LimitToUserList" defaultValue="0" label="Enable (1) or Disable (0)"/>
      </presentation>
    </presentationTable>
  </resources>
</policyDefinitionResources>
