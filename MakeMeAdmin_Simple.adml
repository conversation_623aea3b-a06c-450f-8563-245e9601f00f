<?xml version="1.0" encoding="utf-8"?>
<policyDefinitionResources xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://schemas.microsoft.com/GroupPolicy/2006/07/PolicyDefinitions">
  <displayName />
  <description />
  <resources>
    <stringTable>
      <string id="MakeMeAdmin">MakeMeAdmin</string>
      <string id="SessionDuration">Session Duration</string>
      <string id="SessionDuration_Help">Specifies how long a user remains a local administrator in minutes. Default is 15 minutes.</string>
      <string id="LimitUsers">Limit to Specific Users</string>
      <string id="LimitUsers_Help">When enabled, restricts elevation privileges to specific users only.</string>
    </stringTable>
  </resources>
</policyDefinitionResources>
