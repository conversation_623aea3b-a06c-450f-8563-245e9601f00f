<?xml version="1.0" encoding="utf-8"?>
<policyDefinitions revision="1.0" schemaVersion="1.0">
  <policyNamespaces>
    <target namespace="MakeMeAdmin.Policy" prefix="mma"/>
    <using namespace="Microsoft.Policies.Windows"/>
  </policyNamespaces>
  <resources minRequiredRevision="1.0"/>
  <categories>
    <category name="MakeMeAdminCategory" displayName="$(string.MakeMeAdminCategory)" parentCategory="windows:WindowsComponents"/>
  </categories>
  <policies>
    <policy name="SessionDurationInMinutes" class="Machine" displayName="$(string.SessionDurationInMinutes)" explainText="$(string.SessionDurationInMinutes_Explain)" key="Software\Policies\MakeMeAdmin" valueName="SessionDurationInMinutes" presentation="$(presentation.SessionDurationInMinutes)">
      <parentCategory ref="MakeMeAdminCategory"/>
      <supportedOn ref="SUPPORTED_WIN10"/>
      <elements>
        <decimal id="SessionDurationInMinutes" valueName="SessionDurationInMinutes" minValue="1" maxValue="1440"/>
      </elements>
    </policy>
    <policy name="LimitToUserList" class="Machine" displayName="$(string.LimitToUserList)" explainText="$(string.LimitToUserList_Explain)" key="Software\Policies\MakeMeAdmin" presentation="$(presentation.LimitToUserList)">
      <parentCategory ref="MakeMeAdminCategory"/>
      <supportedOn ref="SUPPORTED_WIN10"/>
      <elements>
        <boolean id="LimitToUserList" valueName="LimitToUserList">
          <trueValue>
            <decimal value="1"/>
          </trueValue>
          <falseValue>
            <decimal value="0"/>
          </falseValue>
        </boolean>
      </elements>
    </policy>
  </policies>
  <supportedOn>
    <definitions>
      <definition name="SUPPORTED_WIN10" displayName="$(string.SUPPORTED_WIN10)">
        <or>
          <range ref="products:MicrosoftWindows" minVersionIndex="6.0"/>
        </or>
      </definition>
    </definitions>
  </supportedOn>
  <presentations>
    <presentation id="SessionDurationInMinutes">
      <decimalTextBox refId="SessionDurationInMinutes" defaultValue="15">Session Duration (Minutes):</decimalTextBox>
    </presentation>
    <presentation id="LimitToUserList">
      <checkBox refId="LimitToUserList" defaultChecked="false">Limit to specific users</checkBox>
    </presentation>
  </presentations>
</policyDefinitions>
