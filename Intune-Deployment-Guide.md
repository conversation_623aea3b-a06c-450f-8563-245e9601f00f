# MakeMeAdmin ADMX/ADML Deployment Guide for Microsoft Intune

This guide provides step-by-step instructions for deploying MakeMeAdmin Administrative Templates to Microsoft Intune.

## Prerequisites

- Microsoft Intune subscription
- Global Administrator or Intune Administrator permissions
- Windows 10/11 devices enrolled in Intune
- ADMX/ADML files (MakeMeAdmin.admx and MakeMeAdmin.adml)

## Deployment Methods

### Method 1: Administrative Templates (Recommended)

#### Step 1: Access Microsoft Endpoint Manager Admin Center

1. Sign in to [Microsoft Endpoint Manager admin center](https://endpoint.microsoft.com)
2. Navigate to **Devices** > **Configuration profiles**

#### Step 2: Create Administrative Template Profile

1. Click **+ Create profile**
2. Select:
   - **Platform**: Windows 10 and later
   - **Profile type**: Templates
   - **Template name**: Administrative Templates
3. Click **Create**

#### Step 3: Upload ADMX/ADML Files

1. In the **Basics** tab:
   - **Name**: MakeMeAdmin Administrative Templates
   - **Description**: Group Policy settings for MakeMeAdmin application
   - Click **Next**

2. In the **Configuration settings** tab:
   - Click **Import ADMX files**
   - Upload **MakeMeAdmin.admx** file
   - Upload **MakeMeAdmin.adml** file
   - Wait for processing to complete
   - Click **Next**

#### Step 4: Configure Policy Settings

1. After import, navigate to **Computer Configuration** > **Administrative Templates**
2. Find **MakeMeAdmin Settings** category
3. Configure the following policies:

**Session Duration (Minutes):**
- Click on **Session Duration (Minutes)**
- Set to **Enabled**
- Enter desired duration (e.g., 15, 30, 60 minutes)
- Click **OK**

**Limit to Specific Users:**
- Click on **Limit to Specific Users**
- Set to **Enabled** or **Disabled** as needed
- If enabled, enter **1**; if disabled, enter **0**
- Click **OK**

4. Click **Next**

#### Step 5: Assign to Groups

1. In the **Assignments** tab:
   - Click **+ Add groups**
   - Select target groups (devices or users)
   - Choose assignment type:
     - **Include**: Apply to selected groups
     - **Exclude**: Exclude specific groups
2. Click **Next**

#### Step 6: Review and Create

1. Review all settings in the **Review + create** tab
2. Click **Create** to deploy the profile

### Method 2: Settings Catalog (Alternative)

#### Step 1: Create Settings Catalog Profile

1. In Microsoft Endpoint Manager admin center
2. Go to **Devices** > **Configuration profiles**
3. Click **+ Create profile**
4. Select:
   - **Platform**: Windows 10 and later
   - **Profile type**: Settings catalog
5. Click **Create**

#### Step 2: Add Settings

1. **Basics** tab:
   - **Name**: MakeMeAdmin Settings Catalog
   - **Description**: MakeMeAdmin configuration via Settings Catalog
   - Click **Next**

2. **Configuration settings** tab:
   - Click **+ Add settings**
   - Search for "Administrative Templates"
   - Browse to find MakeMeAdmin settings (if available)
   - Configure as needed
   - Click **Next**

## Verification Steps

### Step 1: Monitor Deployment Status

1. Go to **Devices** > **Configuration profiles**
2. Click on your MakeMeAdmin profile
3. Check **Device status** and **User status** tabs
4. Verify successful deployment to target devices

### Step 2: Verify on Client Device

1. On a target Windows device, open **Registry Editor**
2. Navigate to: `HKEY_LOCAL_MACHINE\SOFTWARE\Policies\MakeMeAdmin`
3. Verify the following values exist:
   ```
   SessionDurationInMinutes (REG_DWORD)
   LimitToUserList (REG_DWORD)
   ```

### Step 3: Test Group Policy Processing

Run the following PowerShell commands on the client device:
```powershell
# Force Group Policy update
Invoke-GPUpdate -Force

# Check applied policies
Get-ItemProperty -Path "HKLM:\SOFTWARE\Policies\MakeMeAdmin" -ErrorAction SilentlyContinue

# View Group Policy results
gpresult /r /scope:computer
```

## Troubleshooting

### Common Issues and Solutions

#### Issue 1: ADMX Files Not Importing
**Symptoms**: Error during ADMX file upload
**Solutions**:
- Verify ADMX/ADML files are valid XML
- Check file size limits (max 1MB per file)
- Ensure ADML file matches ADMX namespace
- Try uploading files one at a time

#### Issue 2: Policies Not Appearing
**Symptoms**: MakeMeAdmin settings not visible after import
**Solutions**:
- Wait 15-30 minutes for processing
- Refresh the admin center page
- Check import status in **Tenant administration** > **Connectors and tokens** > **Windows enrollment**

#### Issue 3: Settings Not Applying to Devices
**Symptoms**: Registry values not created on client devices
**Solutions**:
- Check device enrollment status
- Verify group assignments
- Force policy sync: **Devices** > **All devices** > Select device > **Sync**
- Check device compliance status

#### Issue 4: Conflicting Policies
**Symptoms**: Settings not applying due to conflicts
**Solutions**:
- Check for duplicate policies in other profiles
- Review policy precedence and filtering
- Use **Device configuration** > **Monitor** > **Assignment failures**

## Best Practices

### 1. Testing Strategy
- Deploy to a pilot group first
- Test on different Windows versions
- Verify functionality before broad deployment

### 2. Group Assignment
- Use device groups for computer configuration policies
- Consider using dynamic groups for automatic assignment
- Document group membership criteria

### 3. Monitoring and Maintenance
- Regularly check deployment status
- Monitor for policy conflicts
- Keep ADMX/ADML files updated with application versions

### 4. Documentation
- Document policy settings and rationale
- Maintain change logs for policy updates
- Train helpdesk on troubleshooting steps

## Policy Settings Reference

| Setting | Registry Path | Value Type | Default | Description |
|---------|---------------|------------|---------|-------------|
| Session Duration | `HKLM\SOFTWARE\Policies\MakeMeAdmin\SessionDurationInMinutes` | REG_DWORD | 15 | Duration in minutes |
| Limit to Users | `HKLM\SOFTWARE\Policies\MakeMeAdmin\LimitToUserList` | REG_DWORD | 0 | 1=Enable, 0=Disable |

## Support Resources

- [Microsoft Intune Documentation](https://docs.microsoft.com/en-us/mem/intune/)
- [Administrative Templates in Intune](https://docs.microsoft.com/en-us/mem/intune/configuration/administrative-templates-windows)
- [Troubleshooting ADMX ingestion](https://docs.microsoft.com/en-us/mem/intune/configuration/administrative-templates-import-custom)

## Next Steps

After successful deployment:
1. Monitor policy application across your environment
2. Gather feedback from users and administrators
3. Consider additional MakeMeAdmin configurations
4. Plan for regular policy reviews and updates
