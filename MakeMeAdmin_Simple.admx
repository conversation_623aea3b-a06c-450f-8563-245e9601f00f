<?xml version="1.0" encoding="utf-8"?>
<policyDefinitions xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" revision="1.0" schemaVersion="1.0" xmlns="http://schemas.microsoft.com/GroupPolicy/2006/07/PolicyDefinitions">
  <policyNamespaces>
    <target prefix="makemeadmin" namespace="MakeMeAdmin.Policies" />
    <using prefix="windows" namespace="Microsoft.Policies.Windows" />
  </policyNamespaces>
  <resources minRequiredRevision="1.0" />
  <categories>
    <category name="MakeMeAdmin" displayName="$(string.MakeMeAdmin)" />
  </categories>
  <policies>
    <policy name="SessionDuration" class="Machine" displayName="$(string.SessionDuration)" explainText="$(string.SessionDuration_Help)" key="SOFTWARE\Policies\MakeMeAdmin" valueName="SessionDurationInMinutes">
      <parentCategory ref="MakeMeAdmin" />
      <supportedOn ref="windows:SUPPORTED_WindowsVista" />
      <enabledValue>
        <decimal value="15" />
      </enabledValue>
      <disabledValue>
        <delete />
      </disabledValue>
    </policy>
    <policy name="LimitUsers" class="Machine" displayName="$(string.LimitUsers)" explainText="$(string.LimitUsers_Help)" key="SOFTWARE\Policies\MakeMeAdmin" valueName="LimitToUserList">
      <parentCategory ref="MakeMeAdmin" />
      <supportedOn ref="windows:SUPPORTED_WindowsVista" />
      <enabledValue>
        <decimal value="1" />
      </enabledValue>
      <disabledValue>
        <decimal value="0" />
      </disabledValue>
    </policy>
  </policies>
</policyDefinitions>
