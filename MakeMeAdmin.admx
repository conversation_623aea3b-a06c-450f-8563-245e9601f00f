<?xml version="1.0" encoding="utf-8"?>
<policyDefinitions revision="1.0" schemaVersion="1.0">
  <policyNamespaces>
    <target namespace="MakeMeAdmin.Policy" prefix="mma"/>
    <using namespace="Microsoft.Policies.Windows"/>
  </policyNamespaces>
  <resources minRequiredRevision="1.0"/>
  <categories>
    <category name="MakeMeAdminCategory" displayName="$(string.MakeMeAdminCategory)"/>
  </categories>
  <policies>
    <policy name="SessionDurationInMinutes" class="Machine" displayName="$(string.SessionDurationInMinutes)" explainText="$(string.SessionDurationInMinutes_Explain)" key="Software\Policies\MakeMeAdmin" valueName="SessionDurationInMinutes" presentation="$(presentation.SessionDurationInMinutes)" category="MakeMeAdminCategory">
      <supportedOn ref="SUPPORTED_WIN10"/>
      <enabledValue>
        <decimal value="15"/>
      </enabledValue>
      <disabledValue>
        <delete/>
      </disabledValue>
    </policy>
    <policy name="LimitToUserList" class="Machine" displayName="$(string.LimitToUserList)" explainText="$(string.LimitToUserList_Explain)" key="Software\Policies\MakeMeAdmin" valueName="LimitToUserList" presentation="$(presentation.LimitToUserList)" category="MakeMeAdminCategory">
      <supportedOn ref="SUPPORTED_WIN10"/>
      <enabledValue>
        <decimal value="1"/>
      </enabledValue>
      <disabledValue>
        <decimal value="0"/>
      </disabledValue>
    </policy>
  </policies>
  <supportedOn>
    <supported id="SUPPORTED_WIN10">
      <product>Windows 10</product>
      <product>Windows 11</product>
    </supported>
  </supportedOn>
  <presentations>
    <decimalTextBox id="SessionDurationInMinutes" defaultValue="15" />
    <decimalTextBox id="LimitToUserList" defaultValue="0" />
  </presentations>
</policyDefinitions>
